#!/bin/bash

# Script to compare numpy warnings before and after PR #375 fix
# Usage: ./compare_warnings_fix.sh

echo "Comparing Numpy Warnings: Before vs After PR #375"
echo "=================================================="
echo ""

echo "Testing MAIN branch (before fix):"
echo "----------------------------------"
git checkout main > /dev/null 2>&1
source /opt/miniconda3/bin/activate tbp.monty
python test_warnings_main_branch.py

echo ""
echo ""
echo "Testing FIX branch (after PR #375):"
echo "------------------------------------"
git checkout fix/numpy-warnings-empty-arrays > /dev/null 2>&1
python demonstrate_warnings_fix.py

echo ""
echo "=================================================="
echo "COMPARISON COMPLETE"
echo ""
echo "Key differences:"
echo "- Main branch: Shows 'Mean of empty slice' and 'invalid value' warnings"
echo "- Fix branch: No warnings, returns 0.0 for empty arrays instead of NaN"
echo ""
echo "The fix eliminates numpy runtime warnings while maintaining functionality."
